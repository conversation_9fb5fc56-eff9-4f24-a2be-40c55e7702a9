import SwiftUI

/// Swipeable, bounded week navigation wrapper for the Plans calendar.
/// - Supports horizontal swiping across weeks with a browse window:
///   back 3 weeks; forward limited to [today, today+7 days].
/// - Degrades animations to crossfades with Reduce Motion.
struct SwipeableMealPlanCalendarView: View {
    let plan: MealPlan
    var onSelectSlot: (Date, MealType, [MealSlot]) -> Void
    var onTapManage: () -> Void = {}

    @Environment(\.accessibilityReduceMotion) private var reduceMotion
    @State private var selectedIndex: Int = 0 // 0 = current week
    @State private var showManage: Bool = false
    @State private var manageSlots: [MealSlot] = []

    private let calendar = Calendar.current

    init(plan: MealPlan, onSelectSlot: @escaping (Date, MealType, [MealSlot]) -> Void, onTapManage: @escaping () -> Void = {}) {
        self.plan = plan
        self.onSelectSlot = onSelectSlot
        self.onTapManage = onTapManage
    }

    // MARK: - Date Bounds

    private var todayStart: Date { calendar.startOfDay(for: Date()) }
    private var forwardLimitDate: Date { calendar.date(byAdding: .day, value: 7, to: todayStart)! }
    private var backwardLimitDate: Date { calendar.date(byAdding: .day, value: -21, to: todayStart)! } // back 3 weeks

    // Allowed week indices relative to current week (0). Typically from -3 to max forward idx (0 or 1).
    private var allowedIndices: [Int] {
        // Compute base Monday of current week
        let baseWeek = WeeklyMealPlan(referenceDate: todayStart, calendar: calendar)
        // Determine if forward limit sits in current week (idx 0) or next week (idx 1)
        let nextWeekStart = calendar.date(byAdding: .day, value: 7, to: baseWeek.weekStart)!
        let forwardInNextWeek = forwardLimitDate >= nextWeekStart
        let maxForwardIndex = forwardInNextWeek ? 1 : 0
        let minIndex = -3
        return Array(minIndex...maxForwardIndex)
    }

    // Build a cached index of plan slots by date (yyyy-MM-dd) → meal → slots
    private var planIndex: [String: [MealType: [MealSlot]]] {
        indexPlanByDateAndMeal(plan: plan)
    }

    @State private var expandedWeeks: Set<Int> = [] // Track expanded states per week index

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            headerView
            calendarTabView
        }
        .onChange(of: selectedIndex) { _, newValue in
            clampSelectedIndex(newValue)
        }
        .sheet(isPresented: $showManage) {
            ManageSelectionView(context: .plans(slots: manageSlots)) {
                // Notify parent to refresh plan if needed
                onTapManage()
            }
        }
    }

    @ViewBuilder
    private var headerView: some View {
        let currentWeek = week(for: clampedSelection)
        let title = WeeklyMealPlan.formatRange(currentWeek.weekStart, currentWeek.weekEnd, calendar: calendar)

        WeekIndicatorView(
            title: title,
            canGoPrevious: canGoPrevious,
            canGoNext: canGoNext,
            onPrevious: {
                withAnimation(WeekTransitionAnimator.animation(reduceMotion: reduceMotion)) {
                    goPrevious()
                }
            },
            onNext: {
                withAnimation(WeekTransitionAnimator.animation(reduceMotion: reduceMotion)) {
                    goNext()
                }
            }
        )
        .padding(.horizontal)
        .padding(.top, 8)
    }

    @ViewBuilder
    private var calendarTabView: some View {
        TabView(selection: $selectedIndex) {
            ForEach(allowedIndices, id: \.self) { idx in
                weekView(for: idx)
                    .tag(idx)
            }
        }
        .tabViewStyle(.page(indexDisplayMode: .never))
        .animation(WeekTransitionAnimator.animation(reduceMotion: reduceMotion), value: selectedIndex)
    }

    @ViewBuilder
    private func weekView(for idx: Int) -> some View {
        let weekData = week(for: idx)

        VStack(spacing: 12) {
            if expandedWeeks.contains(idx) {
                expandedView(week: weekData, idx: idx)
            } else {
                collapsedView(week: weekData, idx: idx)
            }
            Spacer(minLength: 0)
        }
    }

    @ViewBuilder
    private func collapsedView(week: WeeklyMealPlan, idx: Int) -> some View {
        Button {
            withAnimation(CalendarExpansionAnimator.animation(reduceMotion: reduceMotion)) {
                _ = expandedWeeks.insert(idx)
            }
        } label: {
            CollapsedCalendarView(
                weekDays: week.days,
                slotsProvider: slotsProvider
            )
            .transition(CalendarExpansionAnimator.transition(reduceMotion: reduceMotion))
        }
        .buttonStyle(.plain)
        .padding(.horizontal)
    }

    @ViewBuilder
    private func expandedView(week: WeeklyMealPlan, idx: Int) -> some View {
        ExpandedCalendarView(
            weekDays: week.days,
            slotsProvider: slotsProvider,
            favoritesProvider: { id in FavoritesStore.shared.isFavorite(id: id) },
            onSelectSlot: { date, meal, slots in
                guard isDateWithinBrowseWindow(date) else { return }
                onSelectSlot(date, meal, slots)
            },
            onTapManage: { presentManage(for: idx) }
        )
        .transition(CalendarExpansionAnimator.transition(reduceMotion: reduceMotion))

        Button {
            withAnimation(CalendarExpansionAnimator.animation(reduceMotion: reduceMotion)) {
                _ = expandedWeeks.remove(idx)
            }
        } label: {
            Label("Collapse", systemImage: "chevron.up")
                .foregroundStyle(.secondary)
                .padding(.vertical, 4)
        }
        .buttonStyle(.plain)
        .accessibilityLabel("Collapse week")
    }

    // MARK: - Helpers

    private var clampedSelection: Int {
        let indices = allowedIndices
        let minIndex = indices.first ?? 0
        let maxIndex = indices.last ?? 0
        return min(max(selectedIndex, minIndex), maxIndex)
    }

    private func slotsProvider(date: Date, meal: MealType) -> [MealSlot] {
        guard isDateWithinBrowseWindow(date) else { return [] }
        let key = dayKey(for: date)
        if let slots = planIndex[key]?[meal], slots.isEmpty == false {
            return slots
        }

        // Fallback: index search in raw plan days to guard against date normalization drift.
        guard let day = plan.days.first(where: { dayKey(for: $0.date) == key }) else { return [] }
        return day.meals.filter { $0.mealType == meal }
    }

    private func clampSelectedIndex(_ newValue: Int) {
        let indices = allowedIndices
        if newValue < (indices.first ?? 0) { selectedIndex = indices.first ?? 0 }
        if newValue > (indices.last ?? 0) { selectedIndex = indices.last ?? 0 }
    }

    private func presentManage(for idx: Int) {
        // Compute slots for the visible week
        let weekData = week(for: idx)
        var collected: [MealSlot] = []
        let meals: [MealType] = [.breakfast, .lunch, .dinner]
        for day in weekData.days {
            for meal in meals {
                collected.append(contentsOf: slotsProvider(date: day, meal: meal))
            }
        }
        self.manageSlots = collected
        self.showManage = true
    }

    private var canGoPrevious: Bool {
        let minIdx = allowedIndices.first ?? 0
        return selectedIndex > minIdx
    }

    private var canGoNext: Bool {
        let maxIdx = allowedIndices.last ?? 0
        return selectedIndex < maxIdx
    }

    private func goPrevious() { if canGoPrevious { selectedIndex -= 1 } }
    private func goNext() { if canGoNext { selectedIndex += 1 } }

    private func week(for index: Int) -> WeeklyMealPlan {
        let baseWeek = WeeklyMealPlan(referenceDate: todayStart, calendar: calendar)
        if let ref = calendar.date(byAdding: .day, value: index * 7, to: baseWeek.weekStart) {
            return WeeklyMealPlan(referenceDate: ref, calendar: calendar)
        } else {
            return baseWeek
        }
    }

    private func dayKey(for date: Date) -> String {
        Self.yyyyMMdd.string(from: date)
    }

    private func isDateWithinBrowseWindow(_ date: Date) -> Bool {
        let d = calendar.startOfDay(for: date)
        return d >= backwardLimitDate && d <= forwardLimitDate
    }

    private func indexPlanByDateAndMeal(plan: MealPlan) -> [String: [MealType: [MealSlot]]] {
        var result: [String: [MealType: [MealSlot]]] = [:]
        for day in plan.days {
            let key = dayKey(for: day.date)
            for slot in day.meals {
                result[key, default: [:]][slot.mealType, default: []].append(slot)
            }
        }
        return result
    }

    private static let yyyyMMdd: DateFormatter = {
        let f = DateFormatter()
        f.dateFormat = "yyyy-MM-dd"
        f.locale = Locale(identifier: "en_US_POSIX")
        f.timeZone = TimeZone(secondsFromGMT: 0)
        return f
    }()
}

#Preview("SwipeableMealPlanCalendarView") {
    let plan = MealPlan(days: [])
    return SwipeableMealPlanCalendarView(plan: plan) { _, _, _ in }
}
